import os

from rebeldotaichallenge.database.pg_vector import LangChainPGVectorStore

# Use environment variable for database URL, fallback to localhost for development
DATABASE_URL = os.getenv(
    "DATABASE_URL", "postgresql+psycopg://langchain:langchain@localhost:6024/langchain"
)

default_collection = LangChainPGVectorStore(
    connection_string=DATABASE_URL,
    collection_name="default",
    pre_delete_collection=False,
)
extended_collection = LangChainPGVectorStore(
    connection_string=DATABASE_URL,
    collection_name="extended",
    pre_delete_collection=False,
)
